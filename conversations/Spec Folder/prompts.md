
# Prompt 1
Please analyze my Echo application (an investment insights platform) and create comprehensive documentation of your findings. Specifically:

1. Review the entire codebase to understand the application's architecture, features, and functionality
2. Document what the application does, including:
   - Core features and capabilities (voice interaction, AI chat, stock analysis, etc.)
   - User interface components and user experience flow
   - Backend services and API integrations (OpenAI, Claude, financial data sources)
   - Data models and storage mechanisms
   - Key technical implementations (voice recognition, text-to-speech, conversation management)
3. Create a detailed specification document in the `spec` folder that captures:
   - Application overview and purpose
   - Feature descriptions
   - Technical architecture
   - User workflows
   - API endpoints and integrations
   - Any notable implementation details

Save your findings as a well-structured markdown document in the spec folder (create the folder if it doesn't exist). The documentation should be comprehensive enough for a new developer to understand what the application does and how it works.

# Prompt 2
I want to transform the Echo (ShareFlix) voice-driven investment platform into native iOS and Android mobile applications using Flutter, while maintaining the existing Node.js backend. The current web application will continue to exist alongside the mobile apps.

Please create a comprehensive migration and development plan with the following components:

## 1. Architecture Analysis & Refactoring Assessment

**Current State Analysis:**
- Analyze the existing frontend implementation in `public/app.js` (3400+ lines), `public/index.html`, and `public/styles.css`
- Document all client-side functionality currently implemented in the web application
- Create a detailed inventory of features including:
  * Voice recording and playback management
  * Stock detection and preview card display
  * Conversation history rendering and grouping
  * Lead information extraction
  * AI thinking animations and visual feedback
  * Predefined prompt handling
  * Session state management

**Logic Separation:**
- Identify which functionality is currently implemented client-side that should be moved to the backend to support multiple client platforms (web + mobile)
- Categorize each feature as:
  * **Presentation Logic** (stays in client): UI rendering, animations, user input handling, visual feedback
  * **Business Logic** (moves to backend): Data processing, lead qualification scoring, conversation grouping, stock detection, context loading
  * **Shared Logic** (needs API contract): Audio processing workflows, conversation formatting, history filtering

## 2. Backend API Enhancement Plan

**API Gap Analysis:**
- Review existing API endpoints in `server.js` and identify which ones need enhancement for mobile client support
- Analyze frontend features that currently contain business logic and should be exposed as REST API endpoints

**New/Enhanced Endpoints Required:**
- **Stock Detection & Preview:**
  * `POST /api/detect-stocks-in-conversation` - Detect stocks from both user message and AI response, return enriched stock data for preview
  * `GET /api/stock-preview/:ticker` - Get complete stock preview data (currently assembled client-side)
  
- **Conversation Management:**
  * `GET /api/conversation-history/grouped` - Return pre-grouped conversations (currently done in `groupConversations()` client-side)
  * `POST /api/conversation/continue/:sessionId` - Resume conversation with full context (currently assembled client-side)
  * `GET /api/conversation/search?query=` - Server-side conversation search with relevance scoring
  
- **Lead Qualification:**
  * `POST /api/lead/extract` - Extract and score lead information from conversation (currently in `extractLeadInfo()` client-side)
  * `GET /api/lead/qualification-score` - Calculate lead qualification score server-side
  
- **Audio Processing:**
  * `POST /api/audio/process-voice-message` - Combined endpoint for speech-to-text, AI response, text-to-speech, and stock detection
  * `GET /api/audio/welcome-message` - Pre-generated welcome audio with caching
  
- **Session & State:**
  * `POST /api/session/create` - Initialize user session with state management
  * `GET /api/session/state` - Retrieve current session state for mobile app restoration
  * `PUT /api/session/state` - Update session state (conversation context, UI state)

**API Design Principles:**
- All endpoints should return structured JSON responses with consistent error handling
- Include pagination for conversation history endpoints
- Implement proper HTTP status codes and error messages
- Add request/response validation using existing middleware
- Support both authenticated and access-password-only modes

## 3. Flutter Mobile Application Architecture

**Project Structure:**
```
flutter_echo/
├── lib/
│   ├── main.dart
│   ├── core/
│   │   ├── api/          # API client and service layer
│   │   ├── models/       # Data models (Stock, Conversation, Lead, etc.)
│   │   ├── constants/    # App constants, colors, strings
│   │   └── utils/        # Helper functions
│   ├── features/
│   │   ├── auth/         # Access control and authentication
│   │   ├── chat/         # Main conversation interface
│   │   ├── voice/        # Voice recording and playback
│   │   ├── stocks/       # Stock preview and detection
│   │   ├── history/      # Conversation history
│   │   └── onboarding/   # Welcome and introduction
│   ├── shared/
│   │   ├── widgets/      # Reusable UI components
│   │   └── theme/        # App theme and styling
│   └── providers/        # State management (Riverpod/Provider)
```

**State Management:**
- Use **Riverpod** or **Provider** for state management (recommend Riverpod for better testability)
- Separate state into:
  * `ConversationProvider` - Chat messages and history
  * `VoiceProvider` - Recording state and audio playback
  * `StockProvider` - Stock detection and preview data
  * `SessionProvider` - User session and authentication
  * `AudioProvider` - TTS playback and audio queue management

**UI Component Mapping (Web → Flutter):**
- Conversation Display → `ListView.builder` with custom `MessageBubble` widgets
- Voice Controls → Custom `VoiceButton` with animation using `AnimatedContainer`
- Stock Preview Card → `Card` widget with `AnimatedOpacity` for fade-in/out
- Predefined Prompts → `GridView` with `PromptButton` widgets
- History Modal → `BottomSheet` or full-screen `Route` with `ExpansionTile` for grouped conversations
- AI Thinking Animation → Custom `CustomPaint` widget or Lottie animation for blob effect
- Text Input → `TextField` with `IconButton` for send action

**Voice Recording & Playback (iOS/Android):**
- Use **flutter_sound** or **record** package for audio recording
- Use **just_audio** or **audioplayers** for playback
- Implement platform-specific permissions handling:
  * iOS: `NSMicrophoneUsageDescription` in Info.plist
  * Android: `RECORD_AUDIO` permission in AndroidManifest.xml
- Handle audio session management for background playback
- Implement audio interruption handling (phone calls, notifications)

**Offline Capability & Sync:**
- Use **sqflite** or **hive** for local database storage
- Cache conversation history locally with sync status flags
- Implement queue system for offline actions (messages, voice recordings)
- Sync strategy:
  * On app launch: Fetch latest conversations
  * On network restore: Upload queued messages
  * Periodic background sync (if user opts in)
- Store audio files temporarily with cleanup strategy

**Mobile-Specific Considerations:**
- **Permissions:** Microphone, storage, notifications
- **Background Audio:** Continue TTS playback when app is backgrounded
- **Push Notifications:** Optional for conversation updates or market alerts
- **Biometric Auth:** Face ID/Touch ID for app access (optional enhancement)
- **Deep Linking:** Support for resuming specific conversations
- **Adaptive UI:** Different layouts for phone vs tablet
- **Platform Differences:** Handle iOS vs Android audio format differences

## 4. Phased Migration Strategy

**Phase 1: Backend API Foundation (Weeks 1-2)**
- Move business logic from frontend to backend:
  * Conversation grouping logic (`groupConversations()`)
  * Lead extraction and scoring (`extractLeadInfo()`)
  * Stock detection integration (already partially backend, enhance response)
- Create new API endpoints listed in section 2
- Ensure backward compatibility with existing web client
- Add comprehensive API documentation (OpenAPI/Swagger)

**Phase 2: Flutter Project Setup & Core Features (Weeks 3-4)**
- Initialize Flutter project with recommended architecture
- Implement API client layer with error handling
- Build core UI components: conversation display, message bubbles, input controls
- Implement access control and session management
- Create basic navigation structure

**Phase 3: Voice Integration (Weeks 5-6)**
- Implement voice recording with platform-specific handling
- Integrate speech-to-text API calls
- Implement TTS playback with queue management
- Add voice button with visual feedback (blob animation)
- Test on both iOS and Android devices

**Phase 4: Advanced Features (Weeks 7-8)**
- Implement stock detection and preview cards
- Build conversation history with grouping and search
- Add predefined prompts functionality
- Implement offline capability and sync
- Add lead qualification display

**Phase 5: Polish & Testing (Weeks 9-10)**
- Platform-specific optimizations (iOS/Android)
- Performance testing and optimization
- User acceptance testing
- Bug fixes and refinements
- App store preparation (screenshots, descriptions, compliance)

**Backward Compatibility Strategy:**
- Maintain existing web client endpoints during migration
- Version API endpoints if breaking changes are necessary (`/api/v2/...`)
- Use feature flags to gradually roll out backend changes
- Test web client against new backend endpoints before mobile launch

**Breaking Changes & Mitigation:**
- If conversation grouping moves to backend, web client needs update to use new endpoint
- Mitigation: Create new endpoint, update web client, deprecate old client-side logic
- If lead scoring changes, ensure consistent scoring between old and new implementations
- Mitigation: Run parallel scoring and validate consistency before switching

## 5. Deliverables

**Documentation:**
1. **Migration Plan Document** (this document) with:
   - Architecture diagrams (current vs. future state)
   - API endpoint specifications with request/response examples
   - Database schema changes (if any)
   - Security considerations for mobile clients

2. **API Specification Document:**
   - OpenAPI/Swagger specification for all new/modified endpoints
   - Authentication and authorization flows
   - Error codes and handling
   - Rate limiting and quota information

3. **Flutter Architecture Document:**
   - Detailed project structure with file organization
   - State management patterns and data flow diagrams
   - Widget tree structure for main screens
   - Navigation flow and routing strategy
   - Dependency injection approach

4. **Frontend-to-Backend Migration Matrix:**
   - Table listing each frontend function/feature
   - Current location (file and line numbers)
   - Destination (new API endpoint or stays client-side)
   - Justification for decision
   - Migration priority (P0, P1, P2)
   - Estimated effort

5. **Mobile Development Guide:**
   - Setup instructions for Flutter development environment
   - Platform-specific configuration (iOS/Android)
   - Third-party package recommendations with justifications
   - Testing strategy (unit, widget, integration tests)
   - CI/CD pipeline recommendations

**Timeline Estimate:**
- **Backend API Enhancement:** 2 weeks
- **Flutter Core Development:** 4 weeks
- **Voice & Advanced Features:** 4 weeks
- **Testing & Polish:** 2 weeks
- **Total:** 12 weeks (3 months) for MVP mobile app

**Success Criteria:**
- Feature parity with web application for core functionality (voice, chat, stock preview, history)
- Voice recording and playback working smoothly on iOS and Android
- Offline capability for viewing conversation history
- App store ready (meets Apple and Google guidelines)
- Performance: <2s response time for AI interactions, <500ms for UI interactions
- No regressions in existing web application functionality

**Additional Considerations:**
- Ensure the voice-first nature is preserved and optimized for mobile (larger voice button, better audio feedback)
- Consider mobile-specific enhancements: haptic feedback, swipe gestures, pull-to-refresh
- Plan for future features: Apple Watch companion app, widgets, Siri shortcuts
- Budget for App Store ($99/year) and Google Play ($25 one-time) developer accounts
- Consider beta testing through TestFlight (iOS) and Google Play Beta before public launch

# Prompt 3
Create a comprehensive OpenAPI 3.0 specification document for all backend API endpoints in the Echo (ShareFlix) voice-driven investment platform. This should include:

1. **Existing Endpoints** (currently in server.js):
   - Authentication & Access Control: `/api/access/verify`, `/api/auth/login`, `/api/auth/register`
   - Voice Processing: `/api/speech-to-text`, `/api/text-to-speech`
   - AI Chat: `/api/chat`, `/api/ai-status`, `/api/ai-health`
   - Stock Detection: `/api/detect-stocks`, `/api/test-stock-detection`
   - Conversation History: `/api/conversation-history`, `/api/conversation-history/download`
   - Lead Management: `/api/save-lead`

2. **New/Enhanced Endpoints** (required for Flutter mobile migration):
   - Conversation Management: `/api/conversation-history/grouped`, `/api/conversation/continue/:sessionId`, `/api/conversation/search`
   - Stock Detection: `/api/detect-stocks-in-conversation`, `/api/stock-preview/:ticker`
   - Lead Qualification: `/api/lead/extract`, `/api/lead/qualification-score/:sessionId`
   - Mobile Audio: `/api/audio/process-voice-message`, `/api/audio/welcome-message`
   - Session Management: `/api/session/create`, `/api/session/state/:sessionId`

For each endpoint, include:
- Complete path with parameters
- HTTP method (GET, POST, PUT, DELETE)
- Request body schema with all required and optional fields
- Response schema for success (200, 201) and error cases (400, 401, 403, 404, 500, 503)
- Authentication requirements (Bearer token, access password, or both)
- Rate limiting specifications
- Example request/response payloads in JSON format
- Description of what the endpoint does and when to use it

Format the specification as a valid OpenAPI 3.0 YAML file that can be imported into Swagger UI or Postman. Include proper schema definitions, reusable components, security schemes, and server configurations for both development (localhost:3000) and production environments.

Save the complete OpenAPI specification to `spec/openapi-specification.yaml`.

#Prompt 4
I want to rebuild the Echo app using Flutter and start a new repository for this project. Please review all documentation in the `/Users/<USER>/Documents/GitHub/echo/spec/` folder and perform a comprehensive gap analysis to ensure we have everything needed for a successful rebuild.

Specifically, please:

1. **Review all specification documents** in the spec folder to understand the current app requirements and architecture
2. **Identify missing documentation** that would be needed for a Flutter rebuild, such as:
   - API specifications and endpoint documentation
   - Data models and schema definitions
   - State management architecture (we prefer Riverpod based on previous conversations)
   - Authentication and authorization flows
   - Voice functionality specifications (recording, playback, TTS)
   - Stock data integration requirements
   - Chat/conversation history requirements
   - UI/UX specifications and design guidelines
   - Environment configuration and secrets management

3. **Analyze testing coverage** and identify gaps:
   - Unit testing requirements and strategy
   - Integration testing approach
   - Widget testing for Flutter components
   - End-to-end testing scenarios
   - Voice functionality testing
   - API integration testing
   - Performance testing requirements

4. **Conduct security analysis** following OWASP best practices:
   - Authentication and session management security
   - API key and secrets storage
   - Data encryption (in transit and at rest)
   - Input validation and sanitization
   - Authorization and access control
   - Secure communication with backend APIs
   - Privacy considerations for voice data and chat history
   - Third-party dependency security

5. **Provide a summary report** with:
   - List of existing documentation and what it covers
   - Critical gaps that must be addressed before starting the rebuild
   - Recommended additions to the specification folder
   - Security concerns and mitigation strategies
   - Testing strategy recommendations
   - Suggested next steps for preparing the new repository

Please be thorough in your analysis and flag any areas where the current documentation is ambiguous or incomplete.

#Prompt 5 
Based on the comprehensive gap analysis you just completed, I need you to update and enhance the existing migration plan documentation to address all identified gaps and ensure it's production-ready for the Flutter rebuild.

Specifically, please:

1. **Review the existing migration plan** (`spec/flutter-migration-plan.md`) and identify which sections need updates based on the gap analysis findings

2. **Add missing critical sections** to the migration plan:
   - Testing infrastructure setup and implementation timeline
   - Mobile security implementation milestones
   - Performance validation checkpoints
   - Data schema migration strategy
   - Offline functionality implementation phases
   - Error handling implementation approach
   - Development workflow and quality gates

3. **Update the timeline** to include the 4-5 week gap resolution phase before Flutter development begins

4. **Enhance each phase** with:
   - Specific deliverables and acceptance criteria
   - Testing requirements for each milestone
   - Security validation checkpoints
   - Performance benchmarks to meet
   - Risk mitigation strategies

5. **Add detailed task breakdowns** for:
   - Week-by-week activities during gap resolution phase
   - Testing infrastructure setup tasks
   - Mobile security implementation tasks
   - Performance monitoring setup

6. **Include quality gates** at each phase transition with clear go/no-go criteria

7. **Cross-reference** the new specification documents you created (testing strategy, mobile security, performance requirements) so developers know where to find detailed implementation guidance

The updated migration plan should be comprehensive enough that a development team can follow it step-by-step from current state to production-ready Flutter apps, with clear success criteria at each milestone.