openapi: 3.0.3
info:
  title: Echo (ShareFlix) Voice-Driven Investment Platform API
  description: |
    Comprehensive API specification for the Echo voice-driven investment discovery platform.
    Supports both web and mobile Flutter applications with voice processing, AI chat,
    stock detection, and lead qualification capabilities.
  version: 2.0.0
  contact:
    name: Echo API Support
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://shareflix.com/license

servers:
  - url: http://localhost:3000
    description: Development server
  - url: https://api.shareflix.com
    description: Production server
  - url: https://staging-api.shareflix.com
    description: Staging server

security:
  - AccessPassword: []
  - BearerAuth: []
  - AccessPassword: []
    BearerAuth: []

paths:
  # Authentication & Access Control
  /api/access/verify:
    post:
      tags:
        - Authentication
      summary: Verify application access password
      description: Validates the application-level access password and creates an access session
      operationId: verifyAccess
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccessVerifyRequest'
            example:
              password: "echo2025"
      responses:
        '200':
          description: Access granted successfully
          headers:
            Set-Cookie:
              description: Access token cookie
              schema:
                type: string
                example: "accessToken=abc123; HttpOnly; Secure; SameSite=Strict"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessVerifyResponse'
              example:
                success: true
                message: "Access granted"
                expiresIn: 86400000
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '400':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/access/status:
    get:
      tags:
        - Authentication
      summary: Check access session status
      description: Returns current access session status and statistics
      operationId: getAccessStatus
      security: []
      responses:
        '200':
          description: Access status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessStatusResponse'
              example:
                hasAccess: true
                stats:
                  activeSessions: 5
                  totalRequests: 1250
                timestamp: "2025-01-01T12:00:00Z"

  /api/auth/register:
    post:
      tags:
        - Authentication
      summary: Register new user account
      description: Creates a new user account with username, email, and password
      operationId: registerUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistrationRequest'
            example:
              username: "johndoe"
              email: "<EMAIL>"
              password: "SecurePassword123!"
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRegistrationResponse'
              example:
                success: true
                message: "User created successfully"
                user:
                  id: "user_123"
                  username: "johndoe"
                  email: "<EMAIL>"
                  role: "user"
        '400':
          $ref: '#/components/responses/ValidationError'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "User with this email already exists"
                timestamp: "2025-01-01T12:00:00Z"

  /api/auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticates user credentials and returns JWT token
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
            example:
              username: "johndoe"
              password: "SecurePassword123!"
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserLoginResponse'
              example:
                success: true
                message: "Login successful"
                token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                user:
                  id: "user_123"
                  username: "johndoe"
                  email: "<EMAIL>"
                  role: "user"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '400':
          $ref: '#/components/responses/ValidationError'

  # Voice Processing
  /api/speech-to-text:
    post:
      tags:
        - Voice Processing
      summary: Convert speech to text
      description: Transcribes audio file using OpenAI Whisper API
      operationId: speechToText
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                audio:
                  type: string
                  format: binary
                  description: Audio file (WAV, MP3, OGG, WebM)
                language:
                  type: string
                  default: "en"
                  description: Language code for transcription
              required:
                - audio
      responses:
        '200':
          description: Transcription successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpeechToTextResponse'
              example:
                transcription: "What stocks should I buy for long-term growth?"
                success: true
                provider: "openai"
                confidence: 0.95
                language: "en"
                processingTime: "1.2s"
        '400':
          $ref: '#/components/responses/ValidationError'
        '413':
          description: File too large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "Audio file exceeds maximum size limit"
                timestamp: "2025-01-01T12:00:00Z"
        '503':
          $ref: '#/components/responses/ServiceUnavailableError'

  /api/text-to-speech:
    post:
      tags:
        - Voice Processing
      summary: Convert text to speech
      description: Generates audio from text using OpenAI TTS API
      operationId: textToSpeech
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TextToSpeechRequest'
            example:
              text: "Based on your investment goals, I'd recommend looking at technology stocks."
              voice: "alloy"
              speed: 1.0
      responses:
        '200':
          description: Audio generated successfully
          headers:
            Content-Type:
              description: Audio format
              schema:
                type: string
                example: "audio/mpeg"
            Content-Length:
              description: Audio file size in bytes
              schema:
                type: integer
                example: 156789
          content:
            audio/mpeg:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/ValidationError'
        '503':
          $ref: '#/components/responses/ServiceUnavailableError'

  # AI Chat
  /api/chat:
    post:
      tags:
        - AI Chat
      summary: Send message to AI assistant
      description: Processes user message and returns AI response with lead qualification and stock detection
      operationId: chatWithAI
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
            example:
              message: "I want to invest $100,000 for retirement"
              context: "stock_business_lead"
              leadData:
                portfolioSize: 50000
                experience: "beginner"
              conversationHistory:
                - role: "user"
                  content: "Hello"
                - role: "assistant"
                  content: "Hi! How can I help with your investment goals?"
              sessionId: "session_123"
      responses:
        '200':
          description: AI response generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
              example:
                success: true
                response: "That's a substantial portfolio for retirement planning. Let's discuss your timeline and risk tolerance."
                provider: "openai"
                fallback: false
                detectedStocks: []
                leadData:
                  portfolioSize: 100000
                  investmentGoal: "retirement"
                  qualificationScore: 75
                processingTime: "2.1s"
        '400':
          $ref: '#/components/responses/ValidationError'
        '429':
          $ref: '#/components/responses/RateLimitError'
        '503':
          $ref: '#/components/responses/ServiceUnavailableError'

  /api/ai-status:
    get:
      tags:
        - AI Chat
      summary: Get AI service status
      description: Returns current AI service configuration and provider status
      operationId: getAIStatus
      responses:
        '200':
          description: AI status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIStatusResponse'
              example:
                success: true
                primaryProvider: "openai"
                enableFallback: true
                mockMode: false
                lastUsedProvider: "openai"
                fallbackReason: null
                openaiAvailable: true
                claudeAvailable: true

  /api/ai-health:
    get:
      tags:
        - AI Chat
      summary: Check AI provider health
      description: Performs health check on specified AI provider
      operationId: checkAIHealth
      parameters:
        - name: provider
          in: query
          description: AI provider to check (openai or claude)
          required: false
          schema:
            type: string
            enum: [openai, claude]
            default: openai
      responses:
        '200':
          description: Health check completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIHealthResponse'
              example:
                success: true
                status: "healthy"
                provider: "openai"
                responseTime: "0.5s"

  # Stock Detection
  /api/detect-stocks:
    post:
      tags:
        - Stock Detection
      summary: Detect stocks in text
      description: Analyzes text to identify stock mentions and returns stock information
      operationId: detectStocks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StockDetectionRequest'
            example:
              text: "I think Apple and Tesla are good investments"
              includePreview: true
      responses:
        '200':
          description: Stock detection completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StockDetectionResponse'
              example:
                success: true
                detectedStocks:
                  - ticker: "AAPL"
                    companyName: "Apple Inc."
                    confidence: 0.95
                    matchedTerms: ["Apple"]
                    price: 189.84
                    changePercent: 2.34
                processingTime: "0.023s"
        '400':
          $ref: '#/components/responses/ValidationError'
        '503':
          $ref: '#/components/responses/ServiceUnavailableError'

  /api/detect-stocks-in-conversation:
    post:
      tags:
        - Stock Detection
      summary: Detect stocks in conversation context
      description: Enhanced stock detection with conversation context and preview data
      operationId: detectStocksInConversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationStockDetectionRequest'
            example:
              userMessage: "What do you think about Apple and Tesla?"
              aiResponse: "Apple (AAPL) and Tesla (TSLA) are both strong companies..."
              includePreviewData: true
      responses:
        '200':
          description: Enhanced stock detection completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnhancedStockDetectionResponse'
              example:
                success: true
                detectedStocks:
                  - ticker: "AAPL"
                    companyName: "Apple Inc."
                    price: 189.84
                    changePercent: 2.34
                    marketCap: "2.89T"
                    matchDetails:
                      confidence: 0.95
                      source: "ai_response"
                      matchedTerms: ["Apple", "AAPL"]
                      position: 15
                    previewData:
                      priceTarget: 220.00
                      analystRating: "Buy"
                      peRatio: 28.5
                      dividendYield: 0.52
                      thesis: "Leading consumer technology company..."
                processingTime: "0.023s"
        '400':
          $ref: '#/components/responses/ValidationError'
        '503':
          $ref: '#/components/responses/ServiceUnavailableError'

  /api/stock-preview/{ticker}:
    get:
      tags:
        - Stock Detection
      summary: Get stock preview data
      description: Returns detailed stock information for preview cards
      operationId: getStockPreview
      parameters:
        - name: ticker
          in: path
          required: true
          description: Stock ticker symbol
          schema:
            type: string
            pattern: '^[A-Z]{1,5}$'
        - name: includeNews
          in: query
          description: Include recent news
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Stock preview data retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StockPreviewResponse'
              example:
                success: true
                stock:
                  ticker: "AAPL"
                  companyName: "Apple Inc."
                  price: 189.84
                  changePercent: 2.34
                  changeAmount: 4.35
                  marketCap: "2.89T"
                  volume: "45.2M"
                  priceTarget: 220.00
                  analystRating: "Buy"
                  peRatio: 28.5
                  dividendYield: 0.52
                  sector: "Technology"
                  industry: "Consumer Electronics"
                  thesis: "Leading consumer technology company with strong ecosystem..."
                  keyMetrics:
                    revenue: "394.3B"
                    netIncome: "99.8B"
                    eps: 6.16
                    bookValue: 4.84
                  recentNews:
                    - headline: "Apple Reports Strong Q4 Earnings"
                      summary: "Revenue beats expectations..."
                      publishedAt: "2025-01-01T09:00:00Z"
                      source: "Reuters"
                  lastUpdated: "2025-01-01T16:00:00Z"
        '404':
          $ref: '#/components/responses/NotFoundError'
        '503':
          $ref: '#/components/responses/ServiceUnavailableError'

  /api/test-stock-detection:
    get:
      tags:
        - Stock Detection
      summary: Test stock detection service
      description: Health check for stock detection functionality
      operationId: testStockDetection
      responses:
        '200':
          description: Stock detection service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  timestamp:
                    type: string
                    format: date-time
              example:
                success: true
                message: "Stock detection service is operational"
                timestamp: "2025-01-01T12:00:00Z"

  # Conversation Management
  /api/conversation-history:
    get:
      tags:
        - Conversation Management
      summary: Get conversation history
      description: Returns user's conversation history with pagination
      operationId: getConversationHistory
      parameters:
        - name: limit
          in: query
          description: Number of conversations to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of conversations to skip
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Conversation history retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationHistoryResponse'
              example:
                success: true
                conversations:
                  - id: "conv_123"
                    timestamp: "2025-01-01T10:00:00Z"
                    userMessage: "I want to start investing"
                    aiResponse: "That's great! Let's discuss your goals..."
                    detectedStocks: []
                    leadData:
                      portfolioSize: 50000
                      experience: "beginner"
                pagination:
                  total: 45
                  limit: 20
                  offset: 0
                  hasMore: true
        '401':
          $ref: '#/components/responses/UnauthorizedError'
    delete:
      tags:
        - Conversation Management
      summary: Delete conversation history
      description: Deletes all conversation history for the user
      operationId: deleteConversationHistory
      responses:
        '200':
          description: Conversation history deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
              example:
                success: true
                message: "Conversation history deleted successfully"
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /api/conversation-history/grouped:
    get:
      tags:
        - Conversation Management
      summary: Get grouped conversation history
      description: Returns conversations grouped by session with metadata
      operationId: getGroupedConversationHistory
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Grouped conversation history retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupedConversationHistoryResponse'
              example:
                success: true
                conversationGroups:
                  - sessionId: "session_123"
                    title: "Investment Goals Discussion"
                    preview: "I'm interested in long-term growth strategies..."
                    startTime: "2025-01-01T10:00:00Z"
                    endTime: "2025-01-01T10:30:00Z"
                    messageCount: 8
                    leadScore: 75
                    tags: ["stocks", "retirement", "growth"]
                    messages:
                      - id: "msg_001"
                        timestamp: "2025-01-01T10:00:00Z"
                        userMessage: "I want to start investing"
                        aiResponse: "That's great! Let's discuss your goals..."
                        detectedStocks: []
                        leadData:
                          experience: "beginner"
                          confidence: 0.8
                pagination:
                  total: 25
                  limit: 50
                  offset: 0
                  hasMore: false

  /api/conversation-history/download:
    get:
      tags:
        - Conversation Management
      summary: Download conversation history
      description: Downloads conversation history as a text file
      operationId: downloadConversationHistory
      responses:
        '200':
          description: Conversation history file
          headers:
            Content-Disposition:
              description: File download header
              schema:
                type: string
                example: "attachment; filename=conversation-history.txt"
          content:
            text/plain:
              schema:
                type: string
                format: binary
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /api/conversation/continue/{sessionId}:
    post:
      tags:
        - Conversation Management
      summary: Resume conversation
      description: Loads conversation context and prepares to continue
      operationId: continueConversation
      parameters:
        - name: sessionId
          in: path
          required: true
          description: Session identifier
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sessionId:
                  type: string
              required:
                - sessionId
            example:
              sessionId: "session_123"
      responses:
        '200':
          description: Conversation context loaded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContinueConversationResponse'
              example:
                success: true
                conversationContext:
                  - role: "user"
                    content: "I want to invest in tech stocks"
                  - role: "assistant"
                    content: "Technology stocks can be great for growth..."
                leadData:
                  name: "John Doe"
                  portfolioSize: 100000
                  experience: "intermediate"
                  qualificationScore: 75
                sessionState:
                  currentTopic: "tech_stocks"
                  nextSuggestedQuestions:
                    - "What's your risk tolerance?"
                    - "Are you interested in dividend stocks?"
        '404':
          $ref: '#/components/responses/NotFoundError'

  /api/conversation/search:
    get:
      tags:
        - Conversation Management
      summary: Search conversations
      description: Searches conversation history for specific terms
      operationId: searchConversations
      parameters:
        - name: query
          in: query
          required: true
          description: Search query
          schema:
            type: string
            minLength: 1
            maxLength: 100
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: includeContext
          in: query
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: Search results retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationSearchResponse'
              example:
                success: true
                results:
                  - sessionId: "session_456"
                    messageId: "msg_789"
                    relevanceScore: 0.95
                    matchedText: "I think Apple (AAPL) is a solid investment"
                    context:
                      userMessage: "What do you think about Apple?"
                      aiResponse: "Apple (AAPL) is a solid long-term investment..."
                      timestamp: "2025-01-01T15:30:00Z"
                    detectedStocks:
                      - ticker: "AAPL"
                        confidence: 0.95
                searchMetadata:
                  query: "apple"
                  totalResults: 5
                  searchTime: "0.045s"

  # Lead Qualification
  /api/save-lead:
    post:
      tags:
        - Lead Qualification
      summary: Save lead information
      description: Saves extracted lead information to the system
      operationId: saveLead
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveLeadRequest'
            example:
              leadData:
                name: "John Doe"
                email: "<EMAIL>"
                portfolioSize: 100000
                experience: "intermediate"
                investmentGoals: "retirement planning"
                qualificationScore: 85
              sessionId: "session_123"
              source: "voice_conversation"
      responses:
        '201':
          description: Lead saved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveLeadResponse'
              example:
                success: true
                leadId: "lead_456"
                message: "Lead information saved successfully"
        '400':
          $ref: '#/components/responses/ValidationError'

  /api/lead/extract:
    post:
      tags:
        - Lead Qualification
      summary: Extract lead information
      description: Analyzes conversation to extract lead qualification data
      operationId: extractLeadInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LeadExtractionRequest'
            example:
              userMessage: "I have about $100,000 to invest for retirement"
              aiResponse: "That's a substantial portfolio for retirement planning..."
              conversationHistory:
                - role: "user"
                  content: "I'm 45 years old and want to retire at 65"
              sessionId: "session_123"
      responses:
        '200':
          description: Lead information extracted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeadExtractionResponse'
              example:
                success: true
                extractedData:
                  portfolioSize:
                    value: 100000
                    confidence: 0.9
                    source: "user_message"
                  investmentGoal:
                    value: "retirement"
                    confidence: 0.95
                    source: "user_message"
                  timeline:
                    value: "long_term"
                    confidence: 0.8
                    source: "conversation_context"
                  age:
                    value: 45
                    confidence: 0.9
                    source: "conversation_history"
                qualificationScore: 85
                scoreBreakdown:
                  portfolioSize: 30
                  experience: 20
                  engagement: 25
                  timeline: 10
                recommendation: "high_priority"
                nextQuestions:
                  - "What's your current investment experience?"
                  - "How would you describe your risk tolerance?"
                  - "Do you currently work with a financial advisor?"
                processingTime: "0.156s"

  /api/lead/qualification-score/{sessionId}:
    get:
      tags:
        - Lead Qualification
      summary: Get lead qualification score
      description: Returns current qualification score and history for a session
      operationId: getLeadQualificationScore
      parameters:
        - name: sessionId
          in: path
          required: true
          description: Session identifier
          schema:
            type: string
      responses:
        '200':
          description: Qualification score retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeadQualificationScoreResponse'
              example:
                success: true
                sessionId: "session_123"
                qualificationScore: 85
                scoreHistory:
                  - score: 45
                    timestamp: "2025-01-01T10:00:00Z"
                    trigger: "initial_contact"
                  - score: 65
                    timestamp: "2025-01-01T10:15:00Z"
                    trigger: "portfolio_size_revealed"
                  - score: 85
                    timestamp: "2025-01-01T10:30:00Z"
                    trigger: "investment_goals_clarified"
                factors:
                  portfolioSize:
                    score: 30
                    weight: 0.35
                    value: 100000
                    category: "substantial"
                  experience:
                    score: 20
                    weight: 0.25
                    value: "intermediate"
                    category: "some_experience"
                  engagement:
                    score: 25
                    weight: 0.25
                    value: "high"
                    category: "actively_asking_questions"
                  timeline:
                    score: 10
                    weight: 0.15
                    value: "long_term"
                    category: "retirement_planning"
                recommendation:
                  priority: "high_priority"
                  action: "schedule_consultation"
                  reasoning: "Substantial portfolio with clear investment goals and high engagement"
        '404':
          $ref: '#/components/responses/NotFoundError'

  # Mobile Audio Processing
  /api/audio/process-voice-message:
    post:
      tags:
        - Mobile Audio
      summary: Process complete voice message
      description: Combined endpoint for speech-to-text, AI response, and text-to-speech
      operationId: processVoiceMessage
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                audio:
                  type: string
                  format: binary
                  description: Audio file
                sessionId:
                  type: string
                  description: Session identifier
                includeStockDetection:
                  type: boolean
                  default: true
                includeTTS:
                  type: boolean
                  default: true
              required:
                - audio
                - sessionId
      responses:
        '200':
          description: Voice message processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessVoiceMessageResponse'
              example:
                success: true
                transcription:
                  text: "What stocks should I buy for long-term growth?"
                  confidence: 0.95
                  language: "en"
                  processingTime: "1.2s"
                aiResponse:
                  text: "For long-term growth, I'd recommend looking at technology stocks like Apple (AAPL) and Microsoft (MSFT)..."
                  provider: "openai"
                  processingTime: "2.1s"
                audioResponse:
                  url: "/api/audio/response/abc123.mp3"
                  duration: 15.5
                  format: "mp3"
                  size: 248576
                detectedStocks:
                  - ticker: "AAPL"
                    confidence: 0.95
                    previewData:
                      price: 189.84
                      changePercent: 2.34
                  - ticker: "MSFT"
                    confidence: 0.90
                    previewData:
                      price: 415.26
                      changePercent: 1.87
                leadData:
                  extractedInfo:
                    investmentGoal: "long_term_growth"
                  qualificationScore: 70
                sessionUpdated: true
                totalProcessingTime: "3.8s"

  /api/audio/welcome-message:
    get:
      tags:
        - Mobile Audio
      summary: Get cached welcome message
      description: Returns pre-generated welcome audio message
      operationId: getWelcomeMessage
      parameters:
        - name: voice
          in: query
          description: Voice model to use
          schema:
            type: string
            enum: [alloy, echo, fable, onyx, nova, shimmer]
            default: alloy
        - name: format
          in: query
          description: Audio format
          schema:
            type: string
            enum: [mp3, wav, ogg]
            default: mp3
      responses:
        '200':
          description: Welcome audio message
          headers:
            Content-Type:
              description: Audio format
              schema:
                type: string
                example: "audio/mpeg"
            Content-Length:
              description: Audio file size in bytes
              schema:
                type: integer
                example: 156789
            Cache-Control:
              description: Cache control header
              schema:
                type: string
                example: "public, max-age=3600"
            ETag:
              description: Entity tag for caching
              schema:
                type: string
                example: "welcome-v1-alloy-mp3"
          content:
            audio/mpeg:
              schema:
                type: string
                format: binary

  # Session Management
  /api/session/create:
    post:
      tags:
        - Session Management
      summary: Create new session
      description: Creates a new mobile session with device information
      operationId: createSession
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSessionRequest'
            example:
              deviceId: "device_abc123"
              platform: "ios"
              appVersion: "1.0.0"
      responses:
        '201':
          description: Session created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSessionResponse'
              example:
                success: true
                sessionId: "session_def456"
                sessionToken: "jwt_session_token"
                expiresAt: "2025-01-02T10:00:00Z"
                userPreferences:
                  voiceEnabled: true
                  notificationsEnabled: false

  /api/session/state/{sessionId}:
    put:
      tags:
        - Session Management
      summary: Update session state
      description: Updates session state with conversation context and preferences
      operationId: updateSessionState
      parameters:
        - name: sessionId
          in: path
          required: true
          description: Session identifier
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSessionStateRequest'
            example:
              conversationContext:
                - role: "user"
                  content: "I want to invest in tech stocks"
              leadData:
                portfolioSize: 100000
                experience: "intermediate"
              uiState:
                lastScreen: "chat"
                voiceEnabled: true
                currentTopic: "tech_stocks"
              preferences:
                notificationsEnabled: true
                autoPlayTTS: true
      responses:
        '200':
          description: Session state updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSessionStateResponse'
              example:
                success: true
                sessionId: "session_123"
                stateUpdated: true
                timestamp: "2025-01-01T16:30:00Z"
        '404':
          $ref: '#/components/responses/NotFoundError'
    get:
      tags:
        - Session Management
      summary: Get session state
      description: Retrieves current session state and context
      operationId: getSessionState
      parameters:
        - name: sessionId
          in: path
          required: true
          description: Session identifier
          schema:
            type: string
      responses:
        '200':
          description: Session state retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSessionStateResponse'
              example:
                success: true
                sessionId: "session_123"
                sessionState:
                  conversationContext:
                    - role: "user"
                      content: "I want to invest in tech stocks"
                    - role: "assistant"
                      content: "Technology stocks can be great for growth..."
                  leadData:
                    portfolioSize: 100000
                    experience: "intermediate"
                  uiState:
                    lastScreen: "chat"
                    voiceEnabled: true
                    currentTopic: "tech_stocks"
                    scrollPosition: 0
                  preferences:
                    notificationsEnabled: true
                    autoPlayTTS: true
                    voiceSpeed: 1.0
                lastUpdated: "2025-01-01T16:30:00Z"
                expiresAt: "2025-01-02T16:30:00Z"
        '404':
          $ref: '#/components/responses/NotFoundError'

components:
  securitySchemes:
    AccessPassword:
      type: apiKey
      in: cookie
      name: accessToken
      description: Application-level access token
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for user authentication

  schemas:
    # Request Schemas
    AccessVerifyRequest:
      type: object
      required:
        - password
      properties:
        password:
          type: string
          description: Application access password
          minLength: 1
          maxLength: 100

    UserRegistrationRequest:
      type: object
      required:
        - username
        - email
        - password
      properties:
        username:
          type: string
          description: Unique username
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-Z0-9_]+$'
        email:
          type: string
          format: email
          description: User email address
        password:
          type: string
          description: User password
          minLength: 8
          maxLength: 128

    UserLoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          description: Username or email
        password:
          type: string
          description: User password

    TextToSpeechRequest:
      type: object
      required:
        - text
      properties:
        text:
          type: string
          description: Text to convert to speech
          maxLength: 4096
        voice:
          type: string
          enum: [alloy, echo, fable, onyx, nova, shimmer]
          default: alloy
          description: Voice model to use
        speed:
          type: number
          minimum: 0.25
          maximum: 4.0
          default: 1.0
          description: Speech speed multiplier

    ChatRequest:
      type: object
      required:
        - message
      properties:
        message:
          type: string
          description: User message
          maxLength: 4096
        context:
          type: string
          description: Conversation context
          default: "stock_business_lead"
        leadData:
          $ref: '#/components/schemas/LeadData'
        conversationHistory:
          type: array
          items:
            $ref: '#/components/schemas/ConversationMessage'
          maxItems: 20
        sessionId:
          type: string
          description: Session identifier

    StockDetectionRequest:
      type: object
      required:
        - text
      properties:
        text:
          type: string
          description: Text to analyze for stock mentions
          maxLength: 4096
        includePreview:
          type: boolean
          default: false
          description: Include stock preview data

    ConversationStockDetectionRequest:
      type: object
      required:
        - userMessage
        - aiResponse
      properties:
        userMessage:
          type: string
          description: User's message
          maxLength: 4096
        aiResponse:
          type: string
          description: AI's response
          maxLength: 4096
        includePreviewData:
          type: boolean
          default: true
          description: Include detailed stock preview data

    SaveLeadRequest:
      type: object
      required:
        - leadData
      properties:
        leadData:
          $ref: '#/components/schemas/LeadData'
        sessionId:
          type: string
          description: Session identifier
        source:
          type: string
          enum: [voice_conversation, text_chat, manual_entry]
          description: Source of lead information

    LeadExtractionRequest:
      type: object
      required:
        - userMessage
        - aiResponse
      properties:
        userMessage:
          type: string
          description: User's message
          maxLength: 4096
        aiResponse:
          type: string
          description: AI's response
          maxLength: 4096
        conversationHistory:
          type: array
          items:
            $ref: '#/components/schemas/ConversationMessage'
          maxItems: 20
        sessionId:
          type: string
          description: Session identifier

    CreateSessionRequest:
      type: object
      required:
        - deviceId
        - platform
        - appVersion
      properties:
        deviceId:
          type: string
          description: Unique device identifier
          maxLength: 100
        platform:
          type: string
          enum: [ios, android]
          description: Mobile platform
        appVersion:
          type: string
          description: Application version
          pattern: '^\d+\.\d+\.\d+$'

    UpdateSessionStateRequest:
      type: object
      properties:
        conversationContext:
          type: array
          items:
            $ref: '#/components/schemas/ConversationMessage'
          maxItems: 50
        leadData:
          $ref: '#/components/schemas/LeadData'
        uiState:
          type: object
          properties:
            lastScreen:
              type: string
              enum: [chat, history, onboarding, settings]
            voiceEnabled:
              type: boolean
            currentTopic:
              type: string
            scrollPosition:
              type: number
        preferences:
          type: object
          properties:
            notificationsEnabled:
              type: boolean
            autoPlayTTS:
              type: boolean
            voiceSpeed:
              type: number
              minimum: 0.5
              maximum: 2.0

    # Response Schemas
    AccessVerifyResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        expiresIn:
          type: integer
          description: Session expiration time in milliseconds

    AccessStatusResponse:
      type: object
      properties:
        hasAccess:
          type: boolean
        stats:
          type: object
          properties:
            activeSessions:
              type: integer
            totalRequests:
              type: integer
        timestamp:
          type: string
          format: date-time

    UserRegistrationResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        user:
          $ref: '#/components/schemas/User'

    UserLoginResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        token:
          type: string
          description: JWT authentication token
        user:
          $ref: '#/components/schemas/User'

    SpeechToTextResponse:
      type: object
      properties:
        transcription:
          type: string
          description: Transcribed text
        success:
          type: boolean
        provider:
          type: string
          description: AI provider used
        confidence:
          type: number
          minimum: 0
          maximum: 1
          description: Transcription confidence score
        language:
          type: string
          description: Detected language
        processingTime:
          type: string
          description: Processing duration

    ChatResponse:
      type: object
      properties:
        success:
          type: boolean
        response:
          type: string
          description: AI assistant response
        provider:
          type: string
          description: AI provider used
        fallback:
          type: boolean
          description: Whether fallback provider was used
        fallbackReason:
          type: string
          description: Reason for fallback if applicable
        detectedStocks:
          type: array
          items:
            $ref: '#/components/schemas/DetectedStock'
        leadData:
          $ref: '#/components/schemas/LeadData'
        processingTime:
          type: string
          description: Processing duration

    AIStatusResponse:
      type: object
      properties:
        success:
          type: boolean
        primaryProvider:
          type: string
          enum: [openai, claude]
        enableFallback:
          type: boolean
        mockMode:
          type: boolean
        lastUsedProvider:
          type: string
        fallbackReason:
          type: string
          nullable: true
        openaiAvailable:
          type: boolean
        claudeAvailable:
          type: boolean

    AIHealthResponse:
      type: object
      properties:
        success:
          type: boolean
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
        provider:
          type: string
        responseTime:
          type: string
        error:
          type: string
          nullable: true

    StockDetectionResponse:
      type: object
      properties:
        success:
          type: boolean
        detectedStocks:
          type: array
          items:
            $ref: '#/components/schemas/DetectedStock'
        processingTime:
          type: string
          description: Processing duration

    EnhancedStockDetectionResponse:
      type: object
      properties:
        success:
          type: boolean
        detectedStocks:
          type: array
          items:
            $ref: '#/components/schemas/EnhancedDetectedStock'
        processingTime:
          type: string
          description: Processing duration

    StockPreviewResponse:
      type: object
      properties:
        success:
          type: boolean
        stock:
          $ref: '#/components/schemas/StockPreview'

    ConversationHistoryResponse:
      type: object
      properties:
        success:
          type: boolean
        conversations:
          type: array
          items:
            $ref: '#/components/schemas/ConversationHistoryItem'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    GroupedConversationHistoryResponse:
      type: object
      properties:
        success:
          type: boolean
        conversationGroups:
          type: array
          items:
            $ref: '#/components/schemas/ConversationGroup'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    ContinueConversationResponse:
      type: object
      properties:
        success:
          type: boolean
        conversationContext:
          type: array
          items:
            $ref: '#/components/schemas/ConversationMessage'
        leadData:
          $ref: '#/components/schemas/LeadData'
        sessionState:
          type: object
          properties:
            currentTopic:
              type: string
            nextSuggestedQuestions:
              type: array
              items:
                type: string

    ConversationSearchResponse:
      type: object
      properties:
        success:
          type: boolean
        results:
          type: array
          items:
            $ref: '#/components/schemas/ConversationSearchResult'
        searchMetadata:
          type: object
          properties:
            query:
              type: string
            totalResults:
              type: integer
            searchTime:
              type: string

    SaveLeadResponse:
      type: object
      properties:
        success:
          type: boolean
        leadId:
          type: string
          description: Unique lead identifier
        message:
          type: string

    LeadExtractionResponse:
      type: object
      properties:
        success:
          type: boolean
        extractedData:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ExtractedDataItem'
        qualificationScore:
          type: number
          minimum: 0
          maximum: 100
        scoreBreakdown:
          type: object
          properties:
            portfolioSize:
              type: number
            experience:
              type: number
            engagement:
              type: number
            timeline:
              type: number
        recommendation:
          type: string
          enum: [high_priority, medium_priority, low_priority]
        nextQuestions:
          type: array
          items:
            type: string
        processingTime:
          type: string

    LeadQualificationScoreResponse:
      type: object
      properties:
        success:
          type: boolean
        sessionId:
          type: string
        qualificationScore:
          type: number
          minimum: 0
          maximum: 100
        scoreHistory:
          type: array
          items:
            $ref: '#/components/schemas/ScoreHistoryItem'
        factors:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/QualificationFactor'
        recommendation:
          $ref: '#/components/schemas/LeadRecommendation'

    ProcessVoiceMessageResponse:
      type: object
      properties:
        success:
          type: boolean
        transcription:
          $ref: '#/components/schemas/TranscriptionResult'
        aiResponse:
          $ref: '#/components/schemas/AIResponseResult'
        audioResponse:
          $ref: '#/components/schemas/AudioResponseResult'
        detectedStocks:
          type: array
          items:
            $ref: '#/components/schemas/DetectedStock'
        leadData:
          type: object
          properties:
            extractedInfo:
              type: object
              additionalProperties: true
            qualificationScore:
              type: number
        sessionUpdated:
          type: boolean
        totalProcessingTime:
          type: string

    CreateSessionResponse:
      type: object
      properties:
        success:
          type: boolean
        sessionId:
          type: string
        sessionToken:
          type: string
          description: JWT session token
        expiresAt:
          type: string
          format: date-time
        userPreferences:
          type: object
          properties:
            voiceEnabled:
              type: boolean
            notificationsEnabled:
              type: boolean

    UpdateSessionStateResponse:
      type: object
      properties:
        success:
          type: boolean
        sessionId:
          type: string
        stateUpdated:
          type: boolean
        timestamp:
          type: string
          format: date-time

    GetSessionStateResponse:
      type: object
      properties:
        success:
          type: boolean
        sessionId:
          type: string
        sessionState:
          type: object
          properties:
            conversationContext:
              type: array
              items:
                $ref: '#/components/schemas/ConversationMessage'
            leadData:
              $ref: '#/components/schemas/LeadData'
            uiState:
              type: object
              additionalProperties: true
            preferences:
              type: object
              additionalProperties: true
        lastUpdated:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time

    # Data Models
    User:
      type: object
      properties:
        id:
          type: string
          description: Unique user identifier
        username:
          type: string
          description: Username
        email:
          type: string
          format: email
          description: User email
        role:
          type: string
          enum: [user, admin]
          description: User role

    LeadData:
      type: object
      properties:
        name:
          type: string
          description: Contact name
        email:
          type: string
          format: email
          description: Contact email
        phone:
          type: string
          description: Contact phone number
        portfolioSize:
          type: number
          description: Investment portfolio size
        experience:
          type: string
          enum: [beginner, intermediate, advanced]
          description: Investment experience level
        investmentGoals:
          type: string
          description: Investment objectives
        riskTolerance:
          type: string
          enum: [conservative, moderate, aggressive]
          description: Risk tolerance level
        timeline:
          type: string
          enum: [short-term, medium-term, long-term]
          description: Investment timeline
        hasAdvisor:
          type: boolean
          description: Currently has financial advisor
        qualificationScore:
          type: number
          minimum: 0
          maximum: 100
          description: Lead qualification score

    ConversationMessage:
      type: object
      required:
        - role
        - content
      properties:
        role:
          type: string
          enum: [user, assistant, system]
          description: Message role
        content:
          type: string
          description: Message content

    DetectedStock:
      type: object
      properties:
        ticker:
          type: string
          description: Stock ticker symbol
        companyName:
          type: string
          description: Company name
        price:
          type: number
          description: Current stock price
        changePercent:
          type: number
          description: Price change percentage
        marketCap:
          type: string
          description: Market capitalization
        matchDetails:
          type: object
          properties:
            confidence:
              type: number
              minimum: 0
              maximum: 1
            source:
              type: string
              enum: [user_message, ai_response]
            matchedTerms:
              type: array
              items:
                type: string
            position:
              type: integer

    EnhancedDetectedStock:
      allOf:
        - $ref: '#/components/schemas/DetectedStock'
        - type: object
          properties:
            previewData:
              $ref: '#/components/schemas/StockPreviewData'

    StockPreview:
      type: object
      properties:
        ticker:
          type: string
        companyName:
          type: string
        price:
          type: number
        changePercent:
          type: number
        changeAmount:
          type: number
        marketCap:
          type: string
        volume:
          type: string
        priceTarget:
          type: number
        analystRating:
          type: string
          enum: [Buy, Hold, Sell]
        peRatio:
          type: number
        dividendYield:
          type: number
        sector:
          type: string
        industry:
          type: string
        thesis:
          type: string
        keyMetrics:
          type: object
          properties:
            revenue:
              type: string
            netIncome:
              type: string
            eps:
              type: number
            bookValue:
              type: number
        recentNews:
          type: array
          items:
            $ref: '#/components/schemas/NewsItem'
        lastUpdated:
          type: string
          format: date-time

    StockPreviewData:
      type: object
      properties:
        priceTarget:
          type: number
        analystRating:
          type: string
        peRatio:
          type: number
        dividendYield:
          type: number
        thesis:
          type: string

    NewsItem:
      type: object
      properties:
        headline:
          type: string
        summary:
          type: string
        publishedAt:
          type: string
          format: date-time
        source:
          type: string

    ConversationHistoryItem:
      type: object
      properties:
        id:
          type: string
        timestamp:
          type: string
          format: date-time
        userMessage:
          type: string
        aiResponse:
          type: string
        detectedStocks:
          type: array
          items:
            $ref: '#/components/schemas/DetectedStock'
        leadData:
          $ref: '#/components/schemas/LeadData'

    ConversationGroup:
      type: object
      properties:
        sessionId:
          type: string
        title:
          type: string
        preview:
          type: string
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        messageCount:
          type: integer
        leadScore:
          type: number
        tags:
          type: array
          items:
            type: string
        messages:
          type: array
          items:
            $ref: '#/components/schemas/ConversationHistoryItem'

    ConversationSearchResult:
      type: object
      properties:
        sessionId:
          type: string
        messageId:
          type: string
        relevanceScore:
          type: number
          minimum: 0
          maximum: 1
        matchedText:
          type: string
        context:
          type: object
          properties:
            userMessage:
              type: string
            aiResponse:
              type: string
            timestamp:
              type: string
              format: date-time
        detectedStocks:
          type: array
          items:
            $ref: '#/components/schemas/DetectedStock'

    PaginationInfo:
      type: object
      properties:
        total:
          type: integer
        limit:
          type: integer
        offset:
          type: integer
        hasMore:
          type: boolean

    ExtractedDataItem:
      type: object
      properties:
        value:
          oneOf:
            - type: string
            - type: number
            - type: boolean
        confidence:
          type: number
          minimum: 0
          maximum: 1
        source:
          type: string
          enum: [user_message, ai_response, conversation_context, conversation_history]

    ScoreHistoryItem:
      type: object
      properties:
        score:
          type: number
          minimum: 0
          maximum: 100
        timestamp:
          type: string
          format: date-time
        trigger:
          type: string

    QualificationFactor:
      type: object
      properties:
        score:
          type: number
        weight:
          type: number
        value:
          oneOf:
            - type: string
            - type: number
        category:
          type: string

    LeadRecommendation:
      type: object
      properties:
        priority:
          type: string
          enum: [high_priority, medium_priority, low_priority]
        action:
          type: string
        reasoning:
          type: string

    TranscriptionResult:
      type: object
      properties:
        text:
          type: string
        confidence:
          type: number
        language:
          type: string
        processingTime:
          type: string

    AIResponseResult:
      type: object
      properties:
        text:
          type: string
        provider:
          type: string
        processingTime:
          type: string

    AudioResponseResult:
      type: object
      properties:
        url:
          type: string
          format: uri
        duration:
          type: number
        format:
          type: string
        size:
          type: integer

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message
        code:
          type: string
          description: Error code
        details:
          type: object
          description: Additional error details
        timestamp:
          type: string
          format: date-time
        requestId:
          type: string
          description: Request identifier for debugging

  responses:
    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Validation failed"
            code: "VALIDATION_ERROR"
            details:
              field: "email"
              reason: "Invalid email format"
            timestamp: "2025-01-01T12:00:00Z"
            requestId: "req_123"

    UnauthorizedError:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Authentication required"
            code: "UNAUTHORIZED"
            timestamp: "2025-01-01T12:00:00Z"

    ForbiddenError:
      description: Access forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Access forbidden"
            code: "FORBIDDEN"
            timestamp: "2025-01-01T12:00:00Z"

    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Resource not found"
            code: "NOT_FOUND"
            timestamp: "2025-01-01T12:00:00Z"

    RateLimitError:
      description: Rate limit exceeded
      headers:
        X-RateLimit-Limit:
          description: Request limit per time window
          schema:
            type: integer
        X-RateLimit-Remaining:
          description: Remaining requests in current window
          schema:
            type: integer
        X-RateLimit-Reset:
          description: Time when rate limit resets (Unix timestamp)
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Rate limit exceeded"
            code: "RATE_LIMIT_EXCEEDED"
            timestamp: "2025-01-01T12:00:00Z"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "Internal server error"
            code: "INTERNAL_ERROR"
            timestamp: "2025-01-01T12:00:00Z"

    ServiceUnavailableError:
      description: Service temporarily unavailable
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "AI services are temporarily unavailable"
            code: "SERVICE_UNAVAILABLE"
            timestamp: "2025-01-01T12:00:00Z"

tags:
  - name: Authentication
    description: User authentication and access control
  - name: Voice Processing
    description: Speech-to-text and text-to-speech operations
  - name: AI Chat
    description: AI assistant conversation and status
  - name: Stock Detection
    description: Stock mention detection and analysis
  - name: Conversation Management
    description: Conversation history and session management
  - name: Lead Qualification
    description: Lead extraction and qualification scoring
  - name: Mobile Audio
    description: Mobile-optimized audio processing
  - name: Session Management
    description: Mobile session and state management
